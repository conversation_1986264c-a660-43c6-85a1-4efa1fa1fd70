import datetime
from unittest.mock import AsyncMock, Mock, patch

import pytest

from app.integrations.adapters.google_calendar.adapter import GoogleCalendarAdapter
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.schemas import CalendarEventDateTime
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_credentials():
    credentials = Mock(spec=ICredentials)
    credentials.secrets = {
        "access_token": "test_access_token",
        "refresh_token": "test_refresh_token",
        "client_id": "test_client_id",
        "client_secret": "test_client_secret",
        "token_uri": "https://oauth2.googleapis.com/token",
        "scopes": ["https://www.googleapis.com/auth/calendar"],
    }
    return credentials


@pytest.fixture
def mock_client():
    client = AsyncMock()

    # Mock calendar responses
    client.list_calendars.return_value = {
        "items": [
            {
                "id": "primary",
                "summary": "Test Calendar",
                "primary": True,
                "accessRole": "owner",
                "timeZone": "UTC",
            }
        ]
    }

    client.get_calendar.return_value = {
        "id": "primary",
        "summary": "Test Calendar",
        "primary": True,
        "accessRole": "owner",
        "timeZone": "UTC",
    }

    # Mock event responses
    client.list_events.return_value = {
        "items": [
            {
                "id": "event1",
                "summary": "Test Event",
                "description": "Test Description",
                "location": "Test Location",
                "start": {"dateTime": "2024-01-01T10:00:00Z"},
                "end": {"dateTime": "2024-01-01T11:00:00Z"},
                "status": "confirmed",
                "created": "2024-01-01T09:00:00Z",
                "updated": "2024-01-01T09:30:00Z",
                "organizer": {"email": "<EMAIL>", "displayName": "Test User"},
                "attendees": [
                    {
                        "email": "<EMAIL>",
                        "displayName": "Attendee",
                        "responseStatus": "accepted",
                    }
                ],
            }
        ],
        "nextPageToken": None,
    }

    client.get_event.return_value = {
        "id": "event1",
        "summary": "Test Event",
        "description": "Test Description",
        "location": "Test Location",
        "start": {"dateTime": "2024-01-01T10:00:00Z"},
        "end": {"dateTime": "2024-01-01T11:00:00Z"},
        "status": "confirmed",
        "created": "2024-01-01T09:00:00Z",
        "updated": "2024-01-01T09:30:00Z",
        "organizer": {"email": "<EMAIL>", "displayName": "Test User"},
    }

    client.create_event.return_value = {
        "id": "new_event",
        "summary": "New Event",
        "start": {"dateTime": "2024-01-01T10:00:00Z"},
        "end": {"dateTime": "2024-01-01T11:00:00Z"},
        "status": "confirmed",
        "created": "2024-01-01T09:00:00Z",
        "updated": "2024-01-01T09:00:00Z",
        "organizer": {"email": "<EMAIL>"},
    }

    client.update_event.return_value = {
        "id": "event1",
        "summary": "Updated Event",
        "start": {"dateTime": "2024-01-01T10:00:00Z"},
        "end": {"dateTime": "2024-01-01T11:00:00Z"},
        "status": "confirmed",
        "created": "2024-01-01T09:00:00Z",
        "updated": "2024-01-01T09:30:00Z",
        "organizer": {"email": "<EMAIL>"},
    }

    client.delete_event.return_value = None

    client.get_free_busy.return_value = {
        "calendars": {
            "primary": {
                "busy": [
                    {
                        "start": "2024-01-01T10:00:00Z",
                        "end": "2024-01-01T11:00:00Z",
                    }
                ]
            }
        }
    }

    client.get_user_info.return_value = {
        "id": "primary",
        "summary": "Test Calendar",
        "primary": True,
    }

    return client


@pytest.fixture
def google_calendar_adapter(mock_credentials, mock_client):
    with patch("app.integrations.adapters.google_calendar.adapter.GoogleCalendarClient", return_value=mock_client):
        return GoogleCalendarAdapter(mock_credentials)


class TestGoogleCalendarAdapter:
    def test_source_property(self, google_calendar_adapter):
        assert google_calendar_adapter.source == IntegrationSource.GOOGLE_CALENDAR

    def test_init_missing_credentials(self):
        incomplete_credentials = Mock(spec=ICredentials)
        incomplete_credentials.secrets = {"access_token": "test"}

        with pytest.raises(ValueError, match="Google Calendar client_id not found"):
            GoogleCalendarAdapter(incomplete_credentials)

    @pytest.mark.asyncio
    async def test_list_calendars(self, google_calendar_adapter):
        calendars = await google_calendar_adapter.list_calendars()

        assert len(calendars) == 1
        calendar = calendars[0]
        assert calendar.id == "primary"
        assert calendar.name == "Test Calendar"
        assert calendar.is_primary is True
        assert calendar.access_role == "owner"
        assert calendar.timezone == "UTC"

    @pytest.mark.asyncio
    async def test_get_calendar(self, google_calendar_adapter):
        calendar = await google_calendar_adapter.get_calendar("primary")

        assert calendar.id == "primary"
        assert calendar.name == "Test Calendar"
        assert calendar.is_primary is True
        assert calendar.access_role == "owner"

    @pytest.mark.asyncio
    async def test_list_events(self, google_calendar_adapter):
        result = await google_calendar_adapter.list_events("primary")

        assert "events" in result
        assert "next_page_token" in result

        events = result["events"]
        assert len(events) == 1

        event = events[0]
        assert event.id == "event1"
        assert event.title == "Test Event"
        assert event.description == "Test Description"
        assert event.location == "Test Location"
        assert event.calendar_id == "primary"
        assert event.status == "confirmed"
        assert len(event.attendees) == 1
        assert event.attendees[0].email == "<EMAIL>"

    @pytest.mark.asyncio
    async def test_get_event(self, google_calendar_adapter):
        event = await google_calendar_adapter.get_event("primary", "event1")

        assert event.id == "event1"
        assert event.title == "Test Event"
        assert event.calendar_id == "primary"
        assert event.status == "confirmed"

    @pytest.mark.asyncio
    async def test_create_event(self, google_calendar_adapter):
        start = CalendarEventDateTime(dt=datetime.datetime(2024, 1, 1, 10, 0, 0))
        end = CalendarEventDateTime(dt=datetime.datetime(2024, 1, 1, 11, 0, 0))

        event = await google_calendar_adapter.create_event(
            calendar_id="primary",
            title="New Event",
            start=start,
            end=end,
            description="Test Description",
            location="Test Location",
        )

        assert event.id == "new_event"
        assert event.title == "New Event"
        assert event.calendar_id == "primary"

    @pytest.mark.asyncio
    async def test_update_event(self, google_calendar_adapter):
        start = CalendarEventDateTime(dt=datetime.datetime(2024, 1, 1, 10, 0, 0))
        end = CalendarEventDateTime(dt=datetime.datetime(2024, 1, 1, 11, 0, 0))

        event = await google_calendar_adapter.update_event(
            calendar_id="primary",
            event_id="event1",
            title="Updated Event",
            start=start,
            end=end,
        )

        assert event.id == "event1"
        assert event.title == "Updated Event"
        assert event.calendar_id == "primary"

    @pytest.mark.asyncio
    async def test_delete_event(self, google_calendar_adapter):
        result = await google_calendar_adapter.delete_event("primary", "event1")
        assert result is True

    @pytest.mark.asyncio
    async def test_get_free_busy(self, google_calendar_adapter):
        start_time = datetime.datetime(2024, 1, 1, 0, 0, 0)
        end_time = datetime.datetime(2024, 1, 1, 23, 59, 59)

        result = await google_calendar_adapter.get_free_busy(
            calendar_ids=["primary"],
            start_time=start_time,
            end_time=end_time,
        )

        assert "calendars" in result
        assert len(result["calendars"]) == 1

        calendar_info = result["calendars"][0]
        assert calendar_info["calendar_id"] == "primary"
        assert len(calendar_info["busy_periods"]) == 1

    @pytest.mark.asyncio
    async def test_search_events(self, google_calendar_adapter):
        events = await google_calendar_adapter.search_events(
            calendar_id="primary",
            query="Test",
        )

        assert len(events) == 1
        assert events[0].title == "Test Event"

    @pytest.mark.asyncio
    async def test_get_user_info(self, google_calendar_adapter):
        user_info = await google_calendar_adapter.get_user_info()

        assert user_info["id"] == "primary"
        assert user_info["primary"] is True
